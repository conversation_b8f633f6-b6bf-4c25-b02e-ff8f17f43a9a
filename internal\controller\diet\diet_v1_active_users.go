package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) ActiveUsers(ctx context.Context, req *v1.ActiveUsersReq) (res *v1.ActiveUsersRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordActiveUsersInput{
		StartDate: &req.StartDate,
		EndDate:   &req.EndDate,
	}

	// 2. 调用业务逻辑
	output, err := service.Diet().GetActiveUserIds(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.ActiveUsersRes{
		UserIds: output.UserIds,
		Count:   int64(len(output.UserIds)), // 计算用户数量
	}

	return res, nil
}
