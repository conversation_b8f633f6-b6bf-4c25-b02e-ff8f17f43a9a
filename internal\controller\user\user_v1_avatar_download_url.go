package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AvatarDownloadUrl(ctx context.Context, req *v1.AvatarDownloadUrlReq) (res *v1.AvatarDownloadUrlRes, err error) {
	// 1. 从上下文获取当前用户ID（与Java项目保持一致）
	// 这里需要从JWT中间件设置的上下文中获取用户ID
	// 暂时使用模拟数据，实际应该从上下文获取
	userId := int64(1) // TODO: 从上下文获取当前用户ID

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.AvatarDownloadUrlInput{
		UserId: userId,
	}

	// 3. 调用业务逻辑
	output, err := service.User().GenerateAvatarDownloadUrl(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.AvatarDownloadUrlRes{
		AvatarUrl: output.AvatarUrl,
		FileName:  output.FileName,
	}

	return res, nil
}
