package dashboard

import (
	"context"
	"time"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DashboardStats(ctx context.Context, req *v1.DashboardStatsReq) (res *v1.DashboardStatsRes, err error) {
	// 1. 处理日期参数
	var date string
	if req.Date != nil {
		date = req.Date.Format("2006-01-02")
	} else {
		date = time.Now().Format("2006-01-02")
	}

	// 2. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardStatsInput{
		Date: date,
	}

	// 3. 调用业务逻辑
	output, err := service.Dashboard().GetDashboardStats(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 4. 参数转换：业务层Output → API响应
	res = &v1.DashboardStatsRes{
		TotalUsers:               output.Stats.TotalUsers,
		TodayRecords:            output.Stats.TodayRecords,
		NutritionComplianceRate: output.Stats.NutritionComplianceRate,
		RecommendationAccuracy:  output.Stats.RecommendationAccuracy,
		StatisticsDate:          req.Date,
		ActiveUsers:             output.Stats.ActiveUsers,
		WeeklyNewUsers:          output.Stats.WeeklyNewUsers,
		MonthlyNewUsers:         output.Stats.MonthlyNewUsers,
	}

	return res, nil
}
